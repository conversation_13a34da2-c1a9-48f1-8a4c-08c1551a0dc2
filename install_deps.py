#!/usr/bin/env python3
"""
Install compatible dependencies for Fabric Defect Detection Web App
Fixes OpenCV and NumPy compatibility issues with Python 3.12
"""

import subprocess
import sys
import os

def run_command(command):
    """Run a command and return success status"""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ Success: {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running '{command}': {e}")
        print(f"Error output: {e.stderr}")
        return False

def main():
    """Main installation function"""
    print("=" * 60)
    print("🔧 Installing Compatible Dependencies")
    print("=" * 60)
    
    # Check Python version
    python_version = sys.version_info
    print(f"Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major == 3 and python_version.minor >= 12:
        print("⚠️  Python 3.12+ detected - using compatible package versions")
        print("   This will fix OpenCV and NumPy compatibility issues")
    
    print("\n📦 Installing packages...")
    
    # Uninstall problematic packages first
    print("\n🧹 Cleaning up old packages...")
    run_command(f"{sys.executable} -m pip uninstall opencv-python -y")
    run_command(f"{sys.executable} -m pip uninstall opencv-python-headless -y")
    run_command(f"{sys.executable} -m pip uninstall numpy -y")
    
    # Install compatible versions
    print("\n📥 Installing compatible packages...")
    
    packages = [
        "Flask==2.3.3",
        "torch>=2.0.0",
        "torchvision>=0.15.0", 
        "opencv-python-headless==********",
        "numpy>=1.24.0",
        "Pillow>=10.0.0",
        "Werkzeug==2.3.7",
        "ultralytics>=8.0.0"
    ]
    
    for package in packages:
        if not run_command(f"{sys.executable} -m pip install {package}"):
            print(f"⚠️  Failed to install {package}")
    
    print("\n🔍 Testing imports...")
    
    # Test imports
    try:
        import cv2
        print("✅ OpenCV imported successfully")
    except Exception as e:
        print(f"❌ OpenCV import failed: {e}")
    
    try:
        import numpy as np
        print("✅ NumPy imported successfully")
    except Exception as e:
        print(f"❌ NumPy import failed: {e}")
    
    try:
        import torch
        print("✅ PyTorch imported successfully")
    except Exception as e:
        print(f"❌ PyTorch import failed: {e}")
    
    try:
        from PIL import Image
        print("✅ Pillow imported successfully")
    except Exception as e:
        print(f"❌ Pillow import failed: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Installation complete!")
    print("\n🚀 To run the application:")
    print("   python run_simple.py")
    print("\n📚 If you still have issues, try:")
    print("   python install_deps.py")
    print("=" * 60)

if __name__ == "__main__":
    main()
