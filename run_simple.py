#!/usr/bin/env python3
"""
Simple Fabric Defect Detection Web Application
Startup script that avoids OpenCV compatibility issues
"""

import os
import sys

def main():
    """Main startup function"""
    print("=" * 60)
    print("🚀 Fabric Defect Detection Web Application")
    print("=" * 60)
    
    # Check if model exists
    model_path = "./yolov8-fabric-defect-detection/best.pt"
    if os.path.exists(model_path):
        print(f"✅ Model found: {model_path}")
    else:
        print(f"⚠️  Model not found: {model_path}")
        print("   Running in demo mode")
    
    print("\n🔧 Starting web application...")
    print("📱 Open your browser and go to: http://localhost:5000")
    print("📷 Camera mode available at: http://localhost:5000/camera")
    print("\nPress Ctrl+C to stop the application")
    print("=" * 60)
    
    try:
        # Import and run the simple app
        from app_simple import app
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
        return True
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
