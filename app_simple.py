from flask import Flask, render_template, request, jsonify
import os
import base64
from PIL import Image
import io
import numpy as np

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload folder exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Global detector instance (will be None for now)
detector = None

def initialize_detector():
    """Initialize the fabric defect detector"""
    global detector
    model_path = "./yolov8-fabric-defect-detection/best.pt"
    if os.path.exists(model_path):
        try:
            # Try to import the detector
            from fabric_defect_detector import SimpleFabricDetector
            detector = SimpleFabricDetector(model_path, confidence_threshold=0.4)
            print("Detector initialized successfully")
            return True
        except Exception as e:
            print(f"Error initializing detector: {e}")
            print("Running in demo mode without AI detection")
            return False
    else:
        print(f"Model file not found: {model_path}")
        print("Running in demo mode without AI detection")
        return False

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/detect', methods=['POST'])
def detect_defects():
    """Handle image upload and defect detection"""
    if 'image' not in request.files:
        return jsonify({'error': 'No image file provided'}), 400
    
    file = request.files['image']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    try:
        # Read and process image using PIL
        image = Image.open(file.stream)
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Resize image for display
        image.thumbnail((800, 600), Image.Resampling.LANCZOS)
        
        # Convert to base64 for web display
        buffer = io.BytesIO()
        image.save(buffer, format='JPEG')
        result_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        # Demo detection results (since detector is not available)
        if detector is None:
            # Create demo detections
            demo_detections = [
                {
                    'class': 'stain',
                    'confidence': 0.85,
                    'bbox': (100, 100, 200, 150)
                },
                {
                    'class': 'hole',
                    'confidence': 0.92,
                    'bbox': (300, 200, 350, 250)
                }
            ]
            
            defect_counts = {'stain': 1, 'hole': 1}
            total_defects = 2
        else:
            # Use actual detector
            try:
                # Convert PIL image to numpy array for detector
                img_array = np.array(image)
                result_frame, detections = detector.detect(img_array)
                
                # Convert result to base64
                result_buffer = io.BytesIO()
                Image.fromarray(result_frame).save(result_buffer, format='JPEG')
                result_base64 = base64.b64encode(result_buffer.getvalue()).decode('utf-8')
                
                # Prepare detection results
                detection_results = []
                defect_counts = {}
                
                for det in detections:
                    detection_results.append({
                        'class': det['class'],
                        'confidence': float(det['confidence']),
                        'bbox': det['bbox']
                    })
                    
                    # Count defects by type
                    defect_type = det['class'].lower()
                    defect_counts[defect_type] = defect_counts.get(defect_type, 0) + 1
                
                total_defects = len(detections)
                
            except Exception as e:
                print(f"Detection error: {e}")
                # Fallback to demo results
                demo_detections = [
                    {
                        'class': 'demo_defect',
                        'confidence': 0.75,
                        'bbox': (150, 150, 250, 200)
                    }
                ]
                defect_counts = {'demo_defect': 1}
                total_defects = 1
        
        return jsonify({
            'success': True,
            'result_image': result_base64,
            'detections': demo_detections if detector is None else detection_results,
            'total_defects': total_defects,
            'defect_counts': defect_counts,
            'confidence_threshold': 0.4 if detector is None else detector.confidence_threshold,
            'demo_mode': detector is None
        })
        
    except Exception as e:
        return jsonify({'error': f'Processing failed: {str(e)}'}), 500

@app.route('/camera')
def camera():
    """Camera detection page"""
    return render_template('camera.html')

@app.route('/update_confidence', methods=['POST'])
def update_confidence():
    """Update confidence threshold"""
    try:
        data = request.get_json()
        new_confidence = float(data.get('confidence', 0.4))
        
        # Clamp confidence between 0.05 and 0.95
        new_confidence = max(0.05, min(0.95, new_confidence))
        
        if detector is not None:
            detector.confidence_threshold = new_confidence
            detector.model.conf = new_confidence
        
        return jsonify({
            'success': True,
            'confidence': new_confidence
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to update confidence: {str(e)}'}), 500

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'detector_initialized': detector is not None,
        'model_path': "./yolov8-fabric-defect-detection/best.pt",
        'demo_mode': detector is None
    })

if __name__ == '__main__':
    print("🚀 Fabric Defect Detection Web Application")
    print("=" * 50)
    
    # Try to initialize detector
    if initialize_detector():
        print("✅ AI Detection Mode: Full functionality available")
    else:
        print("⚠️  Demo Mode: Running without AI detection")
        print("   - Upload images to see demo results")
        print("   - Install compatible dependencies to enable AI detection")
    
    print("\n🌐 Starting web application...")
    print("📱 Open your browser and go to: http://localhost:5000")
    print("📷 Camera mode available at: http://localhost:5000/camera")
    print("\nPress Ctrl+C to stop the application")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
