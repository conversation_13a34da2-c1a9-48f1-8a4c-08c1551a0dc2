#!/usr/bin/env python3
"""
Fabric Defect Detection Web Application
Startup script for easy launching
"""

import os
import sys
from app import app, initialize_detector

def main():
    """Main startup function"""
    print("=" * 60)
    print("🚀 Fabric Defect Detection Web Application")
    print("=" * 60)
    
    # Check if model exists
    model_path = "./yolov8-fabric-defect-detection/best.pt"
    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        print("Please ensure your YOLOv8 model is placed at the correct path.")
        print("You can also update the model path in app.py")
        return False
    
    print(f"✅ Model found: {model_path}")
    
    # Initialize detector
    print("🔧 Initializing defect detector...")
    if not initialize_detector():
        print("❌ Failed to initialize detector")
        return False
    
    print("✅ Detector initialized successfully")
    print("\n🌐 Starting web application...")
    print("📱 Open your browser and go to: http://localhost:5000")
    print("📷 Camera mode available at: http://localhost:5000/camera")
    print("\nPress Ctrl+C to stop the application")
    print("=" * 60)
    
    try:
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
        return True
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
