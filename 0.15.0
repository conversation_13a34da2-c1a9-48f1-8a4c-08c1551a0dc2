Requirement already satisfied: torchvision in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (0.20.1+cu121)
Collecting numpy (from torchvision)
  Using cached numpy-2.3.2-cp312-cp312-win_amd64.whl.metadata (60 kB)
Requirement already satisfied: torch==2.5.1+cu121 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torchvision) (2.5.1+cu121)
Requirement already satisfied: pillow!=8.3.*,>=5.3.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torchvision) (11.3.0)
Requirement already satisfied: filelock in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torch==2.5.1+cu121->torchvision) (3.19.1)
Requirement already satisfied: typing-extensions>=4.8.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torch==2.5.1+cu121->torchvision) (4.15.0)
Requirement already satisfied: networkx in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torch==2.5.1+cu121->torchvision) (3.5)
Requirement already satisfied: jinja2 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torch==2.5.1+cu121->torchvision) (3.1.6)
Requirement already satisfied: fsspec in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torch==2.5.1+cu121->torchvision) (2025.7.0)
Requirement already satisfied: setuptools in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torch==2.5.1+cu121->torchvision) (80.9.0)
Requirement already satisfied: sympy==1.13.1 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torch==2.5.1+cu121->torchvision) (1.13.1)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from sympy==1.13.1->torch==2.5.1+cu121->torchvision) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from jinja2->torch==2.5.1+cu121->torchvision) (3.0.2)
Downloading numpy-2.3.2-cp312-cp312-win_amd64.whl (12.8 MB)
   ---------------------------------------- 12.8/12.8 MB 701.9 kB/s  0:00:18
Installing collected packages: numpy
Successfully installed numpy-2.3.2
