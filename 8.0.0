Requirement already satisfied: ultralytics in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (8.3.189)
Requirement already satisfied: numpy>=1.23.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from ultralytics) (2.3.2)
Requirement already satisfied: matplotlib>=3.3.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from ultralytics) (3.10.6)
Collecting opencv-python>=4.6.0 (from ultralytics)
  Using cached opencv_python-4.12.0.88-cp37-abi3-win_amd64.whl.metadata (19 kB)
Requirement already satisfied: pillow>=7.1.2 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from ultralytics) (11.3.0)
Requirement already satisfied: pyyaml>=5.3.1 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from ultralytics) (6.0.2)
Requirement already satisfied: requests>=2.23.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from ultralytics) (2.32.5)
Requirement already satisfied: scipy>=1.4.1 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from ultralytics) (1.16.1)
Requirement already satisfied: torch>=1.8.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from ultralytics) (2.5.1+cu121)
Requirement already satisfied: torchvision>=0.9.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from ultralytics) (0.20.1+cu121)
Requirement already satisfied: psutil in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from ultralytics) (7.0.0)
Requirement already satisfied: py-cpuinfo in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from ultralytics) (9.0.0)
Requirement already satisfied: polars in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from ultralytics) (1.32.3)
Requirement already satisfied: ultralytics-thop>=2.0.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from ultralytics) (2.0.16)
Requirement already satisfied: contourpy>=1.0.1 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (1.3.3)
Requirement already satisfied: cycler>=0.10 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (0.12.1)
Requirement already satisfied: fonttools>=4.22.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (4.59.2)
Requirement already satisfied: kiwisolver>=1.3.1 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (1.4.9)
Requirement already satisfied: packaging>=20.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (25.0)
Requirement already satisfied: pyparsing>=2.3.1 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (3.2.3)
Requirement already satisfied: python-dateutil>=2.7 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (2.9.0.post0)
Collecting numpy>=1.23.0 (from ultralytics)
  Using cached numpy-2.2.6-cp312-cp312-win_amd64.whl.metadata (60 kB)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from python-dateutil>=2.7->matplotlib>=3.3.0->ultralytics) (1.17.0)
Requirement already satisfied: charset_normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from requests>=2.23.0->ultralytics) (3.4.3)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from requests>=2.23.0->ultralytics) (3.7)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from requests>=2.23.0->ultralytics) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from requests>=2.23.0->ultralytics) (2025.8.3)
Requirement already satisfied: filelock in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torch>=1.8.0->ultralytics) (3.19.1)
Requirement already satisfied: typing-extensions>=4.8.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torch>=1.8.0->ultralytics) (4.15.0)
Requirement already satisfied: networkx in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torch>=1.8.0->ultralytics) (3.5)
Requirement already satisfied: jinja2 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torch>=1.8.0->ultralytics) (3.1.6)
Requirement already satisfied: fsspec in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torch>=1.8.0->ultralytics) (2025.7.0)
Requirement already satisfied: setuptools in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torch>=1.8.0->ultralytics) (80.9.0)
Requirement already satisfied: sympy==1.13.1 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from torch>=1.8.0->ultralytics) (1.13.1)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from sympy==1.13.1->torch>=1.8.0->ultralytics) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from jinja2->torch>=1.8.0->ultralytics) (3.0.2)
Using cached opencv_python-4.12.0.88-cp37-abi3-win_amd64.whl (39.0 MB)
Using cached numpy-2.2.6-cp312-cp312-win_amd64.whl (12.6 MB)
Installing collected packages: numpy, opencv-python
  Attempting uninstall: numpy
    Found existing installation: numpy 2.3.2
    Uninstalling numpy-2.3.2:
      Successfully uninstalled numpy-2.3.2

Successfully installed numpy-2.2.6 opencv-python-4.12.0.88
